## 芸正医疗 · 任务调度平台（PostgreSQL 版）

### 项目简介
本项目是基于 XXL-JOB 的任务调度平台（PostgreSQL 版本），用于支撑芸正医疗内部与周边系统的定时/异步任务调度与监控告警。仓库内包含调度中心 `xxl-job-admin`、核心库 `xxl-job-core`、以及 Docker 化的一键启动编排（含 PostgreSQL、Hasura、MinIO、示例后端与前端）。

### 架构与组件
- **PostgreSQL 14**：调度中心元数据存储（端口映射 `5434 -> 5432`）。
- **Hasura GraphQL Engine**：可选的 GraphQL 层（端口 `8085`）。
- **rd-scaffold（示例后端）**：示例后端服务（端口 `9528`）。
- **platform-standard（示例前端）**：示例前端（端口 `8080`）。
- **xxl-job-admin**：调度中心 Web 控制台（容器端口 `8898`，宿主机映射 `8089`）。
- **MinIO**：对象存储演示（端口 `9000`）。

### 目录结构（核心）
- `xxl-job-admin/`：调度中心服务源码
- `xxl-job-core/`：核心依赖模块
- `doc/db/tables_xxl_job_postgresql.sql`：PostgreSQL 初始化脚本
- `docker-compose.yml`：本地一键启动编排

## 快速开始（Docker 一键启动）

### 先决条件
- Docker ≥ 20.x
- Docker Compose ≥ v2

### 启动
在仓库根目录执行：

```bash
docker compose up -d
```

首次启动会拉取并启动以下服务：`postgres`、`hasura`、`rd-scaffold`、`platform-standard`、`xxl-job-admin`、`minio`。

### 初始化数据库（PostgreSQL）
当前编排会创建默认库 `postgres`，而调度中心使用库名 `xxl_job`。请按以下步骤初始化：

1) 进入数据库容器创建库：
```bash
docker exec -it postgres psql -U yunzheng -d postgres -c "CREATE DATABASE xxl_job WITH ENCODING 'UTF8';"
```

2) 将初始化脚本导入到新库：
```bash
docker cp doc/db/tables_xxl_job_postgresql.sql postgres:/tmp/xxl_job.sql
docker exec -it postgres psql -U yunzheng -d xxl_job -f /tmp/xxl_job.sql
```

> 若你本机已安装 `psql`，也可直接对宿主机端口 `5434` 执行：
```bash
psql -h 127.0.0.1 -p 5434 -U yunzheng -d postgres -c "CREATE DATABASE xxl_job WITH ENCODING 'UTF8';"
psql -h 127.0.0.1 -p 5434 -U yunzheng -d xxl_job -f doc/db/tables_xxl_job_postgresql.sql
```

### 访问地址
- 调度中心控制台： [XXL-JOB 控制台](http://localhost:8089/xxl-job-admin)
- PostgreSQL：`127.0.0.1:5434`（用户/密码：`yunzheng/yunzheng`）
- Hasura 控制台： [Hasura Console](http://localhost:8085)
- 示例前端： [Platform Standard](http://localhost:8080)
- MinIO 控制台： [MinIO Console](http://localhost:9000)（默认 `minioadmin/minioadmin123`）

### 调度中心默认账号
- 用户名：`admin`
- 密码：`123456`

如需修改默认管理员密码，可在初始化 SQL 导入后，直接在 `xxl_job` 库表 `xxl_job_user` 中更新。

### 环境变量（关键项）
`docker-compose.yml` 中可根据需要覆盖以下变量：
- `DATASOURCE_URL`：`***************************************`
- `DATASOURCE_USERNAME`：`yunzheng`
- `DATASOURCE_PASSWORD`：`yunzheng`
- `CONTEXT_PATH`：`/xxl-job-admin`
- `SERVER_PORT`：`8898`（已映射到宿主 `8089`）

## 本地开发与构建

### 环境要求
- JDK 17
- Maven 3.9+

### 编译
```bash
mvn -v
mvn -U -T 1C clean package -DskipTests
```

### 本地运行调度中心（可选）
如需脱离 Docker 本地启动 `xxl-job-admin`：
1) 在 `xxl-job-admin/src/main/resources/application.properties` 中配置 PostgreSQL 连接（或通过 JVM 参数覆盖）：
```
spring.datasource.url=****************************************
spring.datasource.username=yunzheng
spring.datasource.password=yunzheng
```
2) 启动：
```bash
cd xxl-job-admin
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dserver.port=8898 -Dserver.servlet.context-path=/xxl-job-admin"
```
然后访问 [http://localhost:8898/xxl-job-admin](http://localhost:8898/xxl-job-admin)。

## 数据库脚本说明
- 位置：`doc/db/tables_xxl_job_postgresql.sql`
- 内容包含：
  - 核心表：`xxl_job_group`、`xxl_job_info`、`xxl_job_log`、`xxl_job_log_report`、`xxl_job_logglue`、`xxl_job_lock`、`xxl_job_user`、`xxl_job_registry`
  - 索引/约束初始化
  - 默认数据（管理员、示例执行器与任务）

## 常见问题（FAQ）
- 无法访问控制台：确认容器 `xxl-job-admin` 运行、健康检查通过，且访问路径携带 `CONTEXT_PATH`（默认 `/xxl-job-admin`）。
- 登录失败：确认已初始化 `xxl_job` 数据库并导入脚本；或检查 `DATASOURCE_*` 配置是否正确。
- 端口冲突：若本机已有 PostgreSQL 占用 `5432`，本编排已将宿主端口映射到 `5434`，如仍冲突请在 `docker-compose.yml` 中调整。

## 生产部署建议
- 使用外部托管的高可用 PostgreSQL（开启自动备份、合理的 `max_connections` 与连接池配置）。
- 为 `xxl-job-admin` 配置反向代理与 HTTPS，限制外网访问。
- 启用访问令牌（`XXL_JOB_ACCESS_TOKEN`）并妥善保密。
- 基于业务划分执行器与权限，避免单点故障；开启报警邮箱/企业微信/钉钉等告警渠道。

## 许可证
遵循上游 XXL-JOB 开源协议（GPL-3.0）。


