2025-08-27 17:38:41.755 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 16206 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:38:41.756 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:38:42.246 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:38:42.251 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:38:42.252 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:38:42.252 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:38:42.266 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:38:42.266 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 431 ms
2025-08-27 17:38:42.421 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:38:42.424 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:38:42.427 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:38:42.441 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:38:42.443 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:38:42.514 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@30298421
2025-08-27 17:38:42.515 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:38:42.634 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:38:42.656 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:38:42.662 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:38:42.662 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:38:42.663 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 17:38:42.663 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:38:42.668 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 1.033 seconds (process running for 1.311)
2025-08-27 17:38:47.003 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:40:34.352 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:40:34.357 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:40:34.360 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:40:35.792 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 16415 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:40:35.793 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:40:36.288 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:40:36.292 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:40:36.293 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:40:36.293 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:40:36.306 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:40:36.307 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 434 ms
2025-08-27 17:40:36.454 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:40:36.457 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:40:36.460 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:40:36.471 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:40:36.473 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:40:36.546 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@3dd5785d
2025-08-27 17:40:36.547 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:40:36.660 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:40:36.684 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:40:36.691 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:40:36.691 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:40:36.691 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-27 17:40:36.692 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:40:36.698 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 1.03 seconds (process running for 1.34)
2025-08-27 17:40:41.005 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:41:34.826 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:41:34.831 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:41:34.835 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:41:36.344 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 16580 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:41:36.345 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:41:36.824 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:41:36.831 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:41:36.832 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:41:36.832 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:41:36.850 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:41:36.850 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 442 ms
2025-08-27 17:41:37.017 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:41:37.020 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:41:37.022 [xxl-job, admin JobLogReportHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:41:37.035 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:41:37.037 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:41:37.103 [xxl-job, admin JobLogReportHelper] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@26585880
2025-08-27 17:41:37.104 [xxl-job, admin JobLogReportHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:41:37.221 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:41:37.244 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:41:37.250 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:41:37.250 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:41:37.251 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 17:41:37.251 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:41:37.256 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 1.014 seconds (process running for 1.275)
2025-08-27 17:41:42.003 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:42:19.323 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:42:19.326 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:42:19.332 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:42:20.782 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 16668 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:42:20.783 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:42:21.255 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:42:21.260 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:42:21.260 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:42:21.260 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:42:21.274 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:42:21.274 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 424 ms
2025-08-27 17:42:21.414 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:42:21.417 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:42:21.419 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:42:21.431 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:42:21.433 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:42:21.501 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@a1299c5
2025-08-27 17:42:21.501 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:42:21.611 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:42:21.635 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:42:21.641 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:42:21.641 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:42:21.641 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-27 17:42:21.641 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:42:21.647 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 0.968 seconds (process running for 1.219)
2025-08-27 17:42:26.004 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:42:36.463 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:42:36.465 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:42:36.467 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:42:37.847 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 16704 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:42:37.848 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:42:38.307 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:42:38.312 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:42:38.313 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:42:38.313 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:42:38.326 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:42:38.326 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 402 ms
2025-08-27 17:42:38.463 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:42:38.466 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:42:38.468 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:42:38.481 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:42:38.483 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:42:38.549 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@65068dd4
2025-08-27 17:42:38.550 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:42:38.659 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:42:38.683 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:42:38.689 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:42:38.689 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:42:38.690 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 17:42:38.690 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:42:38.696 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 0.956 seconds (process running for 1.199)
2025-08-27 17:42:43.005 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:45:35.908 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:45:35.911 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:45:35.913 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:45:37.694 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 17022 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:45:37.695 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:45:38.222 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:45:38.227 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:45:38.227 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:45:38.227 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:45:38.242 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:45:38.242 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 469 ms
2025-08-27 17:45:38.407 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:45:38.410 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:45:38.412 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:45:38.424 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:45:38.425 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:45:38.497 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@386637a6
2025-08-27 17:45:38.497 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:45:38.605 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:45:38.628 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:45:38.633 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:45:38.633 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:45:38.634 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 17:45:38.634 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:45:38.640 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 1.057 seconds (process running for 1.312)
2025-08-27 17:45:43.004 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:47:14.769 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:47:14.771 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:47:14.773 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:47:16.367 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 17163 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:47:16.367 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:47:16.992 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:47:16.997 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:47:16.998 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:47:16.998 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:47:17.018 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:47:17.018 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 544 ms
2025-08-27 17:47:17.199 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:47:17.203 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:47:17.206 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:47:17.222 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:47:17.225 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:47:17.298 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@51cab1b8
2025-08-27 17:47:17.298 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:47:17.419 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:47:17.445 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:47:17.450 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:47:17.450 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:47:17.451 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 17:47:17.451 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:47:17.457 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 1.213 seconds (process running for 1.466)
2025-08-27 17:47:22.001 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:54:48.564 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:54:48.566 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:54:48.570 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:54:50.520 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 17919 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:54:50.522 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:54:51.225 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:54:51.229 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:54:51.230 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:54:51.230 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:54:51.245 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:54:51.245 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 604 ms
2025-08-27 17:54:51.408 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:54:51.411 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:54:51.413 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:54:51.425 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:54:51.427 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:54:51.496 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@690746f
2025-08-27 17:54:51.497 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:54:51.606 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:54:51.628 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:54:51.636 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:54:51.636 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:54:51.637 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 17:54:51.637 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:54:51.643 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 1.254 seconds (process running for 1.521)
2025-08-27 17:54:56.004 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:55:12.280 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:55:12.282 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:55:12.285 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:55:14.496 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 17991 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:55:14.497 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:55:15.015 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:55:15.024 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:55:15.025 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:55:15.025 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:55:15.046 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:55:15.046 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 468 ms
2025-08-27 17:55:15.351 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:55:15.354 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:55:15.357 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:55:15.370 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:55:15.372 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:55:15.442 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@5cb946d8
2025-08-27 17:55:15.443 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:55:15.562 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:55:15.584 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:55:15.592 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:55:15.592 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:55:15.593 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-27 17:55:15.593 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:55:15.599 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 1.22 seconds (process running for 1.453)
2025-08-27 17:55:20.006 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:56:08.942 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 17:56:08.944 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 17:56:08.947 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:56:10.466 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 21.0.7 with PID 18092 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job)
2025-08-27 17:56:10.467 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:56:10.946 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:56:10.950 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:56:10.951 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:56:10.951 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:56:10.964 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:56:10.964 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 429 ms
2025-08-27 17:56:11.111 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:56:11.114 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:56:11.118 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:56:11.130 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:56:11.131 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:56:11.201 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@79e8f094
2025-08-27 17:56:11.201 [xxl-job, admin JobFailMonitorHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:56:11.311 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:56:11.336 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:56:11.341 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-27 17:56:11.341 [main] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-27 17:56:11.341 [main] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-08-27 17:56:11.342 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8898 (http) with context path '/xxl-job-admin'
2025-08-27 17:56:11.348 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Started XxlJobAdminApplication in 0.985 seconds (process running for 1.266)
2025-08-27 17:56:16.005 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:58:49.636 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 23.0.2 with PID 18330 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin)
2025-08-27 17:58:49.637 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 17:58:50.159 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 17:58:50.165 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 17:58:50.166 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 17:58:50.166 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 17:58:50.187 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 17:58:50.188 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 467 ms
2025-08-27 17:58:50.363 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 17:58:50.367 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 17:58:50.370 [xxl-job, admin JobLogReportHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 17:58:50.386 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 17:58:50.388 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 17:58:50.472 [xxl-job, admin JobLogReportHelper] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@77d46fa2
2025-08-27 17:58:50.473 [xxl-job, admin JobLogReportHelper] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 17:58:50.555 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 17:58:50.577 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 17:58:50.580 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-27 17:58:50.580 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 17:58:51.585 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 17:58:51.585 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>>>> xxl-job, JobScheduleHelper#scheduleThread stop
2025-08-27 17:58:52.005 [xxl-job, admin JobScheduleHelper#ringThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>>>> xxl-job, JobScheduleHelper#ringThread stop
2025-08-27 17:58:52.590 [main] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>>>> xxl-job, JobScheduleHelper stop
2025-08-27 17:58:52.590 [xxl-job, admin JobLogReportHelper] INFO  c.x.j.a.s.thread.JobLogReportHelper - >>>>>>>>>>> xxl-job, job log report thread stop
2025-08-27 17:58:52.590 [xxl-job, admin JobLosedMonitorHelper] INFO  c.x.j.a.s.thread.JobCompleteHelper - >>>>>>>>>>> xxl-job, JobLosedMonitorHelper stop
2025-08-27 17:58:52.590 [xxl-job, admin JobFailMonitorHelper] INFO  c.x.j.a.s.t.JobFailMonitorHelper - >>>>>>>>>>> xxl-job, job fail monitor thread stop
2025-08-27 17:58:52.591 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  c.x.j.a.s.thread.JobRegistryHelper - >>>>>>>>>>> xxl-job, job registry monitor thread stop
2025-08-27 17:58:52.591 [main] INFO  c.x.j.a.s.t.JobTriggerPoolHelper - >>>>>>>>> xxl-job trigger thread pool shutdown success.
2025-08-27 17:58:52.592 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Shutdown initiated...
2025-08-27 17:58:52.595 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Shutdown completed.
2025-08-27 17:58:52.604 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-27 17:58:52.611 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8898 was already in use.

Action:

Identify and stop the process that's listening on port 8898 or configure this application to listen on another port.

2025-08-27 18:05:25.161 [main] INFO  c.x.job.admin.XxlJobAdminApplication - Starting XxlJobAdminApplication using Java 23.0.2 with PID 18900 (/Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin/target/classes started by fanggl in /Users/<USER>/fileStorage/yunzheng/yz-job/xxl-job-admin)
2025-08-27 18:05:25.162 [main] INFO  c.x.job.admin.XxlJobAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-27 18:05:25.680 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8898 (http)
2025-08-27 18:05:25.687 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8898"]
2025-08-27 18:05:25.688 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-27 18:05:25.688 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-27 18:05:25.713 [main] INFO  o.a.c.c.C.[.[.[/xxl-job-admin] - Initializing Spring embedded WebApplicationContext
2025-08-27 18:05:25.714 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 483 ms
2025-08-27 18:05:25.885 [main] INFO  c.x.j.a.s.scheduler.XxlJobScheduler - >>>>>>>>> init xxl-job admin success.
2025-08-27 18:05:25.889 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap started.
2025-08-27 18:05:25.893 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Starting...
2025-08-27 18:05:25.908 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-08-27 18:05:25.910 [main] INFO  c.x.s.c.a.i.XxlSsoWebInterceptor - XxlSsoWebInterceptor init.
2025-08-27 18:05:25.990 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.pool.HikariPool - HikariCP - Added connection org.postgresql.jdbc.PgConnection@31bff8fd
2025-08-27 18:05:25.991 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Start completed.
2025-08-27 18:05:26.085 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint beneath base path '/actuator'
2025-08-27 18:05:26.105 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8898"]
2025-08-27 18:05:26.108 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-27 18:05:26.108 [main] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
2025-08-27 18:05:27.113 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>> init xxl-job admin scheduler success.
2025-08-27 18:05:27.114 [xxl-job, admin JobScheduleHelper#scheduleThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>>>> xxl-job, JobScheduleHelper#scheduleThread stop
2025-08-27 18:05:28.003 [xxl-job, admin JobScheduleHelper#ringThread] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>>>> xxl-job, JobScheduleHelper#ringThread stop
2025-08-27 18:05:28.115 [main] INFO  c.x.j.a.s.thread.JobScheduleHelper - >>>>>>>>>>> xxl-job, JobScheduleHelper stop
2025-08-27 18:05:28.116 [xxl-job, admin JobLogReportHelper] INFO  c.x.j.a.s.thread.JobLogReportHelper - >>>>>>>>>>> xxl-job, job log report thread stop
2025-08-27 18:05:28.118 [xxl-job, admin JobLosedMonitorHelper] INFO  c.x.j.a.s.thread.JobCompleteHelper - >>>>>>>>>>> xxl-job, JobLosedMonitorHelper stop
2025-08-27 18:05:28.119 [xxl-job, admin JobFailMonitorHelper] INFO  c.x.j.a.s.t.JobFailMonitorHelper - >>>>>>>>>>> xxl-job, job fail monitor thread stop
2025-08-27 18:05:28.120 [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] INFO  c.x.j.a.s.thread.JobRegistryHelper - >>>>>>>>>>> xxl-job, job registry monitor thread stop
2025-08-27 18:05:28.120 [main] INFO  c.x.j.a.s.t.JobTriggerPoolHelper - >>>>>>>>> xxl-job trigger thread pool shutdown success.
2025-08-27 18:05:28.122 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Shutdown initiated...
2025-08-27 18:05:28.127 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariCP - Shutdown completed.
2025-08-27 18:05:28.140 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-27 18:05:28.149 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8898 was already in use.

Action:

Identify and stop the process that's listening on port 8898 or configure this application to listen on another port.

2025-08-27 18:06:26.760 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-27 18:06:26.762 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-27 18:06:26.765 [SpringApplicationShutdownHook] INFO  c.x.s.core.bootstrap.XxlSsoBootstrap - >>>>>>>>>>> xxl-mq XxlSsoBootstrap stopped.
