<#-- page import (style + script) -->
<#macro commonStyle>

	<#-- i18n -->
	<#global I18n = I18nUtil.getMultString()?eval />

	<#-- favicon、logo -->
	<title>${I18n.admin_name}</title>
	<link rel="icon" href="${request.contextPath}/static/favicon.ico" />

	<#-- meta -->
	<meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">

	<#-- link style -->
    <!-- Bootstrap -->
    <link rel="stylesheet" href="${request.contextPath}/static/adminlte/bower_components/bootstrap/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="${request.contextPath}/static/adminlte/bower_components/font-awesome/css/font-awesome.min.css">
    <!-- Ionicons -->
    <link rel="stylesheet" href="${request.contextPath}/static/adminlte/bower_components/Ionicons/css/ionicons.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="${request.contextPath}/static/adminlte/dist/css/AdminLTE.min.css">
    <!-- AdminLTE Skins. Choose a skin from the css/skins folder instead of downloading all of them to reduce the load. -->
    <link rel="stylesheet" href="${request.contextPath}/static/adminlte/dist/css/skins/_all-skins.min.css">
      
	<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

	<!-- pace -->
	<link rel="stylesheet" href="${request.contextPath}/static/adminlte/bower_components/PACE/themes/blue/pace-theme-flash.css">

    <style>
		/* 现代化主页设计风格 - 减少圆角，舒适配色  author: FangGL  date: 2025-08-27 */

		/* 全局背景与基础设置 */
		body { background-color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
		.content-wrapper { background-color: #f8fafc; }

		/* 顶部导航栏 - 简洁现代风格 */
		.skin-blue .main-header .navbar {
			background: linear-gradient(135deg, #2563eb, #1d4ed8);
			border: none;
			box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
		}
		.skin-blue .main-header .logo {
			background-color: #1e40af;
			color: #ffffff;
			border: none;
			font-weight: 600;
		}
		.skin-blue .main-header .logo:hover { background-color: #1e3a8a; }

		/* 左侧边栏 - 现代深色主题 */
		.skin-blue .main-sidebar {
			background: linear-gradient(180deg, #1e293b, #0f172a);
			box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
		}
		.skin-blue .sidebar a { color: #cbd5e1; transition: all 0.2s ease; }
		.skin-blue .sidebar-menu > li > a {
			margin: 2px 8px;
			border-radius: 6px;
			padding: 12px 16px;
			transition: all 0.2s ease;
		}
		.skin-blue .sidebar-menu > li.active > a,
		.skin-blue .sidebar-menu > li > a:hover {
			background-color: rgba(59, 130, 246, 0.15);
			color: #ffffff;
			transform: translateX(2px);
		}
		.skin-blue .sidebar-menu > li.active > a {
			background-color: rgba(59, 130, 246, 0.2);
			border-left: 3px solid #3b82f6;
		}

		/* 内容区域 - 卡片与信息盒子 */
		.content-wrapper .box {
			border-radius: 8px;
			background: #ffffff;
			box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
			border: 1px solid #e5e7eb;
			transition: box-shadow 0.2s ease;
		}
		.content-wrapper .box:hover {
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
		}

		/* 信息盒子重新设计 */
		.info-box {
			border-radius: 8px;
			background: #ffffff;
			box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
			border: 1px solid #e5e7eb;
			transition: all 0.2s ease;
			overflow: hidden;
		}
		.info-box:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
		}

		/* 重新设计信息盒子颜色 */
		.info-box.bg-aqua { background: linear-gradient(135deg, #06b6d4, #0891b2) !important; }
		.info-box.bg-yellow { background: linear-gradient(135deg, #f59e0b, #d97706) !important; }
		.info-box.bg-green { background: linear-gradient(135deg, #10b981, #059669) !important; }

		.info-box-icon {
			background: rgba(255, 255, 255, 0.2) !important;
			color: #ffffff !important;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.info-box-content { color: #ffffff !important; }
		.info-box-text { color: rgba(255, 255, 255, 0.9) !important; font-weight: 500; }
		.info-box-number { color: #ffffff !important; font-weight: 700; }
		.progress-description { color: rgba(255, 255, 255, 0.8) !important; }

		/* 表单与按钮 */
		.form-control {
			border-radius: 6px;
			border-color: #d1d5db;
			transition: border-color 0.2s ease, box-shadow 0.2s ease;
		}
		.form-control:focus {
			border-color: #3b82f6;
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
		}
		.btn-primary {
			border-radius: 6px;
			background: linear-gradient(135deg, #3b82f6, #2563eb);
			border: none;
			font-weight: 500;
			transition: all 0.2s ease;
		}
		.btn-primary:hover, .btn-primary:focus {
			background: linear-gradient(135deg, #2563eb, #1d4ed8);
			transform: translateY(-1px);
			box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
		}

		/* 页面标题 */
		.content-header h1 {
			color: #1f2937;
			font-weight: 600;
			margin-bottom: 0;
		}

		/* 移除页脚后的布局调整 */
		.wrapper {
			min-height: 100vh;
		}
		.content-wrapper {
			padding-bottom: 20px;
		}

		/* 图表容器优化 */
		.box-header {
			background: #f9fafb;
			border-bottom: 1px solid #e5e7eb;
			padding: 16px 20px;
		}
		.box-header .box-title {
			color: #1f2937;
			font-weight: 600;
		}
		.box-body {
			padding: 20px;
		}

		/* 进度条优化 */
		.progress {
			background-color: rgba(255, 255, 255, 0.3);
			border-radius: 4px;
		}
		.progress-bar {
			background-color: rgba(255, 255, 255, 0.8);
			border-radius: 4px;
		}
	</style>

</#macro>

<#macro commonScript>
	<!-- jQuery -->
	<script src="${request.contextPath}/static/adminlte/bower_components/jquery/jquery.min.js"></script>
	<!-- Bootstrap -->
	<script src="${request.contextPath}/static/adminlte/bower_components/bootstrap/js/bootstrap.min.js"></script>
	<!-- PACE -->
	<script src="${request.contextPath}/static/adminlte/bower_components/PACE/pace.min.js"></script>
	<!-- jquery.slimscroll -->
	<script src="${request.contextPath}/static/adminlte/bower_components/jquery-slimscroll/jquery.slimscroll.min.js"></script>
	<!-- FastClick -->
	<script src="${request.contextPath}/static/adminlte/bower_components/fastclick/fastclick.js"></script>

    <#-- jquery cookie -->
	<script src="${request.contextPath}/static/plugins/jquery/jquery.cookie.js"></script>
	<#-- jquery.validate -->
	<script src="${request.contextPath}/static/plugins/jquery/jquery.validate.min.js"></script>
	<#-- layer -->
	<script src="${request.contextPath}/static/plugins/layer/layer.js"></script>

	<!-- base config -->
    <script>
		var base_url = '${request.contextPath}';
        var I18n = ${I18nUtil.getMultString()};
	</script>

	<!-- AdminLTE App -->
	<script src="${request.contextPath}/static/adminlte/dist/js/adminlte.min.js"></script>

	<#-- common js -->
	<script src="${request.contextPath}/static/js/common.1.js"></script>

</#macro>

<#-- page module: Header-->
<#macro commonHeader>
	<header class="main-header">
		<!-- header-logo -->
		<a href="${request.contextPath}/" class="logo">
			<span class="logo-mini"><b>XXL</b></span>
			<span class="logo-lg"><b>${I18n.admin_name}</b></span>
		</a>
		<nav class="navbar navbar-static-top" role="navigation">
			<!--header left -->
			<a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </a>
			<!--header right -->
          	<div class="navbar-custom-menu">
				<ul class="nav navbar-nav">
					<#-- login user -->
                    <li class="dropdown">
                        <a href="javascript:" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false" style="font-weight: bold;">
                            ${I18n.system_welcome}：${xxl_sso_user.userName!}
                            <span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu" role="menu">
                            <li id="updatePwd" ><a href="javascript:">${I18n.change_pwd}</a></li>
                            <li id="logoutBtn" ><a href="javascript:">${I18n.logout_btn}</a></li>
                        </ul>
                    </li>
				</ul>
			</div>

		</nav>
	</header>

	<!-- 修改密码.模态框 -->
	<div class="modal fade" id="updatePwdModal" tabindex="-1" role="dialog"  aria-hidden="true">
		<div class="modal-dialog ">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title" >${I18n.change_pwd}</h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal form" role="form" >
						<div class="form-group">
							<label for="lastname" class="col-sm-2 control-label">${I18n.change_pwd_field_oldpwd}<font color="red">*</font></label>
							<div class="col-sm-10"><input type="text" class="form-control" name="oldPassword" placeholder="${I18n.system_please_input} ${I18n.change_pwd_field_oldpwd}" maxlength="20" ></div>
						</div>
						<div class="form-group">
							<label for="lastname" class="col-sm-2 control-label">${I18n.change_pwd_field_newpwd}<font color="red">*</font></label>
							<div class="col-sm-10"><input type="text" class="form-control" name="password" placeholder="${I18n.system_please_input} ${I18n.change_pwd_field_newpwd}" maxlength="20" ></div>
						</div>
						<hr>
						<div class="form-group">
							<div class="col-sm-offset-3 col-sm-6">
								<button type="submit" class="btn btn-primary"  >${I18n.system_save}</button>
								<button type="button" class="btn btn-default" data-dismiss="modal">${I18n.system_cancel}</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>

</#macro>

<#-- page module: Footer-->
<#macro commonFooter >
	<footer class="main-footer">
		Powered by <b>phoenix</b>
		<div class="pull-right hidden-xs">
			<strong>Copyright &copy; 2025 &nbsp;
<#--			<strong>Copyright &copy; 2025-${.now?string('yyyy')} &nbsp;-->
			</strong><!-- All rights reserved. -->
		</div>
	</footer>
</#macro>

<#-- page module: Left-->
<#macro commonLeft pageName >
	<!-- Left side column. contains the logo and sidebar -->
	<aside class="main-sidebar">
		<!-- sidebar: style can be found in sidebar.less -->
		<section class="sidebar">
			<!-- sidebar menu: : style can be found in sidebar.less -->
			<ul class="sidebar-menu">
                <li class="header">${I18n.system_nav}</li>
                <li class="nav-click <#if pageName == "index">active</#if>" ><a href="${request.contextPath}/"><i class="fa fa-circle-o text-aqua"></i><span>${I18n.job_dashboard_name}</span></a></li>
				<li class="nav-click <#if pageName == "jobinfo">active</#if>" ><a href="${request.contextPath}/jobinfo"><i class="fa fa-circle-o text-yellow"></i><span>${I18n.jobinfo_name}</span></a></li>
				<li class="nav-click <#if pageName == "joblog">active</#if>" ><a href="${request.contextPath}/joblog"><i class="fa fa-circle-o text-green"></i><span>${I18n.joblog_name}</span></a></li>
				<#if xxl_sso_user.roleList?? && xxl_sso_user.roleList?seq_contains("ADMIN") >
                    <li class="nav-click <#if pageName == "jobgroup">active</#if>" ><a href="${request.contextPath}/jobgroup"><i class="fa fa-circle-o text-red"></i><span>${I18n.jobgroup_name}</span></a></li>
                    <li class="nav-click <#if pageName == "user">active</#if>" ><a href="${request.contextPath}/user"><i class="fa fa-circle-o text-purple"></i><span>${I18n.user_manage}</span></a></li>
				</#if>
				<#-- 使用教程入口已移除  author: FangGL  date: 2025-08-27 -->
			</ul>
		</section>
		<!-- /.sidebar -->
	</aside>
</#macro>

<#macro commonControl >
	<!-- Control Sidebar -->
	<aside class="control-sidebar control-sidebar-dark">
		<!-- Create the tabs -->
		<ul class="nav nav-tabs nav-justified control-sidebar-tabs">
			<li class="active"><a href="#control-sidebar-home-tab" data-toggle="tab"><i class="fa fa-home"></i></a></li>
			<li><a href="#control-sidebar-settings-tab" data-toggle="tab"><i class="fa fa-gears"></i></a></li>
		</ul>
		<!-- Tab panes -->
		<div class="tab-content">
			<!-- Home tab content -->
			<div class="tab-pane active" id="control-sidebar-home-tab">
				<h3 class="control-sidebar-heading">近期活动</h3>
				<ul class="control-sidebar-menu">
					<li>
						<a href="javascript::;">
							<i class="menu-icon fa fa-birthday-cake bg-red"></i>
							<div class="menu-info">
								<h4 class="control-sidebar-subheading">张三今天过生日</h4>
								<p>2015-09-10</p>
							</div>
						</a>
					</li>
					<li>
						<a href="javascript::;"> 
							<i class="menu-icon fa fa-user bg-yellow"></i>
							<div class="menu-info">
								<h4 class="control-sidebar-subheading">Frodo 更新了资料</h4>
								<p>更新手机号码 +1(800)1111-1111</p>
							</div>
						</a>
					</li>
					<li>
						<a href="javascript::;"> 
							<i class="menu-icon fa fa-envelope-o bg-light-blue"></i>
							<div class="menu-info">
								<h4 class="control-sidebar-subheading">Nora 加入邮件列表</h4>
								<p><EMAIL></p>
							</div>
						</a>
					</li>
					<li>
						<a href="javascript:;">
						<i class="menu-icon fa fa-file-code-o bg-green"></i>
						<div class="menu-info">
							<h4 class="control-sidebar-subheading">001号定时作业调度</h4>
							<p>5秒前执行</p>
						</div>
						</a>
					</li>
				</ul>
				<!-- /.control-sidebar-menu -->
			</div>
			<!-- /.tab-pane -->

			<!-- Settings tab content -->
			<div class="tab-pane" id="control-sidebar-settings-tab">
				<form method="post">
					<h3 class="control-sidebar-heading">个人设置</h3>
					<div class="form-group">
						<label class="control-sidebar-subheading"> 左侧菜单自适应
							<input type="checkbox" class="pull-right" checked>
						</label>
						<p>左侧菜单栏样式自适应</p>
					</div>
					<!-- /.form-group -->

				</form>
			</div>
			<!-- /.tab-pane -->
		</div>
	</aside>
	<!-- /.control-sidebar -->
	<!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
	<div class="control-sidebar-bg"></div>
</#macro>
