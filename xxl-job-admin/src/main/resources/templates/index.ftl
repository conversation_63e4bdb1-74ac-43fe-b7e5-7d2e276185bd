<!DOCTYPE html>
<html>
<head>
    <#-- import macro -->
  	<#import "./common/common.macro.ftl" as netCommon>
    <#-- commonStyle -->
	<@netCommon.commonStyle />

    <#-- biz start（1/5 style） -->
    <link rel="stylesheet" href="${request.contextPath}/static/adminlte/bower_components/bootstrap-daterangepicker/daterangepicker.css">
    <style>
        /* 主页专属样式增强  author: FangGL  date: 2025-08-27 */

        /* 页面标题区域 */
        .content-header {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            margin-bottom: 20px;
            padding: 24px 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        /* 统计卡片间距优化 */
        .info-box {
            margin-bottom: 20px;
        }

        /* 图表区域增强 */
        .chart-container {
            background: #ffffff;
            border-radius: 8px;
            padding: 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        /* 日期选择器按钮美化 */
        .daterange {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border: none;
            border-radius: 6px;
            color: #ffffff;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .daterange:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        /* 图表包装器 */
        .chart-wrapper {
            position: relative;
            background: #fafbfc;
            border-radius: 6px;
            padding: 10px;
            margin: 5px 0;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .content-header {
                margin: 0 -15px 20px -15px;
                border-radius: 0;
            }
            .info-box {
                margin-bottom: 15px;
            }
            .chart-wrapper {
                margin: 10px 0;
            }
            #lineChart, #pieChart {
                height: 300px !important;
            }
        }
    </style>
    <#-- biz end（1/5 end） -->

</head>
<body class="hold-transition skin-blue sidebar-mini">
<div class="wrapper">

	<!-- header -->
	<@netCommon.commonHeader />

	<!-- left -->
	<@netCommon.commonLeft "index" />

    <!-- right start -->
	<div class="content-wrapper">

        <!-- content-header -->
		<section class="content-header">
            <#-- biz start（3/5 name） -->
			<h1>${I18n.job_dashboard_name}</h1>
            <#-- biz end（3/5 name） -->
		</section>

        <!-- content-main -->
		<section class="content">

            <#-- biz start（4/5 content） -->

            <!-- 任务信息 -->
            <div class="row">

                <#-- 任务信息 -->
                <div class="col-md-4 col-sm-6 col-xs-12">
                    <div class="info-box bg-aqua">
                        <span class="info-box-icon"><i class="fa fa-flag-o"></i></span>

                        <div class="info-box-content">
                            <span class="info-box-text">${I18n.job_dashboard_job_num}</span>
                            <span class="info-box-number">${jobInfoCount}</span>

                            <div class="progress">
                                <div class="progress-bar" style="width: 100%"></div>
                            </div>
                            <span class="progress-description">${I18n.job_dashboard_job_num_tip}</span>
                        </div>
                    </div>
                </div>

                <#-- 调度信息 -->
                <div class="col-md-4 col-sm-6 col-xs-12" >
                    <div class="info-box bg-yellow">
                        <span class="info-box-icon"><i class="fa fa-calendar"></i></span>

                        <div class="info-box-content">
                            <span class="info-box-text">${I18n.job_dashboard_trigger_num}</span>
                            <span class="info-box-number">${jobLogCount}</span>

                            <div class="progress">
                                <div class="progress-bar" style="width: 100%" ></div>
                            </div>
                            <span class="progress-description">
                                ${I18n.job_dashboard_trigger_num_tip}
                                <#--<#if jobLogCount gt 0>
                                    调度成功率：${(jobLogSuccessCount*100/jobLogCount)?string("0.00")}<small>%</small>
                                </#if>-->
                            </span>
                        </div>
                    </div>
                </div>

                <#-- 执行器 -->
                <div class="col-md-4 col-sm-6 col-xs-12">
                    <div class="info-box bg-green">
                        <span class="info-box-icon"><i class="fa ion-ios-settings-strong"></i></span>

                        <div class="info-box-content">
                            <span class="info-box-text">${I18n.job_dashboard_jobgroup_num}</span>
                            <span class="info-box-number">${executorCount}</span>

                            <div class="progress">
                                <div class="progress-bar" style="width: 100%"></div>
                            </div>
                            <span class="progress-description">${I18n.job_dashboard_jobgroup_num_tip}</span>
                        </div>
                    </div>
                </div>

            </div>

            <#-- 调度报表：时间区间筛选，左侧折线图 + 右侧饼图 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box chart-container">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-bar-chart" style="color: #3b82f6; margin-right: 8px;"></i>
                                ${I18n.job_dashboard_report}
                            </h3>

                            <!-- tools box -->
                            <div class="pull-right box-tools">
                                <button type="button" class="btn btn-primary btn-sm daterange pull-right" data-toggle="tooltip" id="filterTime" >
                                    <i class="fa fa-calendar" style="margin-right: 6px;"></i>
                                    选择时间范围
                                </button>
                            </div>
                            <!-- /. tools -->

                        </div>
                        <div class="box-body">
                            <div class="row">
                                <#-- 左侧折线图 -->
                                <div class="col-md-8">
                                    <div class="chart-wrapper">
                                        <div id="lineChart" style="height: 380px;"></div>
                                    </div>
                                </div>
                                <#-- 右侧饼图 -->
                                <div class="col-md-4">
                                    <div class="chart-wrapper">
                                        <div id="pieChart" style="height: 380px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <#-- biz end（4/5 content） -->

		</section>
	</div>
    <!-- right end -->

    <!-- footer -->
	<@netCommon.commonFooter />
</div>
<@netCommon.commonScript />

<#-- biz start（5/5 script） -->

<!-- daterangepicker -->
<script src="${request.contextPath}/static/adminlte/bower_components/moment/moment.min.js"></script>
<script src="${request.contextPath}/static/adminlte/bower_components/bootstrap-daterangepicker/daterangepicker.js"></script>
<#-- echarts -->
<script src="${request.contextPath}/static/plugins/echarts/echarts.common.min.js"></script>
<script src="${request.contextPath}/static/js/index.js"></script>

<#-- biz end（5/5 script） -->
</body>
</html>
