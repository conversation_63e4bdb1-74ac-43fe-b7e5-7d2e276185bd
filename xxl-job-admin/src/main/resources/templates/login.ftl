<!DOCTYPE html>
<#-- 登录页样式优化  author: FangGL  date: 2025-08-27 -->
<html>
<head>
	<#-- import macro -->
  	<#import "./common/common.macro.ftl" as netCommon>

	<#-- commonStyle -->
	<@netCommon.commonStyle />
	<!-- iCheck -->
    <link rel="stylesheet" href="${request.contextPath}/static/adminlte/plugins/iCheck/square/blue.css">
    <style>
        /* 自定义登录页样式（仅作用于登录页，避免全局污染）  author: FangGL  date: 2025-08-27 */
        .login-page { background: linear-gradient(135deg, #f0f4ff, #e6f7ff) fixed; display: flex; align-items: center; justify-content: center; min-height: 100vh; }
        .login-box { width: 380px; max-width: 92%; margin: 0 auto; }
        .login-logo a { color: #1f2d3d; text-decoration: none; font-weight: 700; letter-spacing: .5px; font-size: 28px; }
        .login-box-body { border-radius: 12px; box-shadow: 0 10px 24px rgba(0,0,0,.08); border: 1px solid #eef2f7; padding: 28px 26px 22px 26px; background: #fff; }
        .login-box-msg { margin: 0 0 18px 0; color: #6b7280; font-size: 13px; }
        .form-control { height: 42px; border-radius: 8px; border-color: #e5e7eb; }
        .form-control:focus { border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59,130,246,.15); }
        .glyphicon.form-control-feedback { top: 8px; color: #9ca3af; }
        .icheck label { font-weight: normal; color: #4b5563; }
        .btn-primary.btn-block { border-radius: 22px; height: 40px; font-weight: 600; letter-spacing: .5px; }
        @media (max-height: 640px) { .login-box { margin: 0 auto; } }
    </style>
</head>
<body class="hold-transition login-page">

	<#-- login div -->
	<div class="login-box">

		<form id="loginForm" method="post" >
			<div class="login-logo">
				<a><b>芸正任务调度中心</b></a>
			</div>
			<div class="login-box-body">
				<p class="login-box-msg">Power By phoenix</p>
				<div class="form-group has-feedback">
	            	<input type="text" name="userName" class="form-control" placeholder="${I18n.login_username_placeholder}"  maxlength="20" >
	            	<span class="glyphicon glyphicon-envelope form-control-feedback"></span>
				</div>
	          	<div class="form-group has-feedback">
	            	<input type="password" name="password" class="form-control" placeholder="${I18n.login_password_placeholder}"  maxlength="20" >
	            	<span class="glyphicon glyphicon-lock form-control-feedback"></span>
	          	</div>
				<div class="row">
					<div class="col-xs-8">
		              	<div class="checkbox icheck">
		                	<label>
		                  		<input type="checkbox" name="ifRemember" > &nbsp; ${I18n.login_remember_me}
		                	</label>
						</div>
		            </div><!-- /.col -->
		            <div class="col-xs-4">
						<button type="submit" class="btn btn-primary btn-block btn-flat">${I18n.login_btn}</button>
					</div>
				</div>
			</div>
		</form>
	</div>

<@netCommon.commonScript />
<!-- icheck -->
<script src="${request.contextPath}/static/adminlte/plugins/iCheck/icheck.min.js"></script>
<!-- js file -->
<script src="${request.contextPath}/static/js/login.1.js"></script>

</body>
</html>
