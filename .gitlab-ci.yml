# XXL-Job GitLab CI/CD 配置
# 作者: FangGL
# 日期: 2024-12-27

stages:
  - build

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: 1
  # 定义镜像名称，用于管理后台
  HARBOR_IMAGE_NAME: "xxl-job-admin"

# 构建并推送Docker镜像
docker-build-push:
  stage: build
  image: docker:latest
  timeout: 2h
  tags:
    - cloud-dev
  services:
    - name: docker:dind
      alias: docker
      command: ["--insecure-registry=$HARBOR_REGISTRY"]
  before_script:
    # 配置BuildKit支持非安全镜像仓库
    - mkdir -p ~/.docker
    - |
      cat > ~/.docker/config.json << EOF
      {
        "experimental": "enabled",
        "insecure-registries": ["$HARBOR_REGISTRY"]
      }
      EOF
    # 登录Harbor镜像仓库
    - echo $HARBOR_PASSWORD | docker login $HARBOR_REGISTRY -u $HARBOR_USERNAME --password-stdin
  script:
    # 构建Docker镜像（带commit SHA标签）
    - docker build -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA .
    
    # 给同一个镜像打latest标签
    - docker tag $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    
    # 推送两个标签到镜像仓库
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    
    # 输出成功信息
    - echo "成功推送 $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA"
    - echo "成功推送 $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest"
  only:
    - dev
    - main
