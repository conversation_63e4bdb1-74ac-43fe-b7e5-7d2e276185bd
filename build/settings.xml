<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库路径 -->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <!-- 镜像配置 - 使用阿里云镜像加速下载 -->
    <mirrors>
        <mirror>
            <id>aliyun-maven</id>
            <name><PERSON><PERSON> Maven Mirror</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <!-- JDK 版本配置 -->
        <profile>
            <id>jdk-17</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>17</jdk>
            </activation>
            <properties>
                <maven.compiler.source>17</maven.compiler.source>
                <maven.compiler.target>17</maven.compiler.target>
                <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>
            </properties>
        </profile>
    </profiles>

    <!-- 激活的配置文件 -->
    <activeProfiles>
        <activeProfile>jdk-17</activeProfile>
    </activeProfiles>

</settings>
