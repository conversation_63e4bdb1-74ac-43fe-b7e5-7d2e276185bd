# XXL-Job Admin Dockerfile
# 作者: FangGL
# 日期: 2024-12-27

# 使用OpenJDK 17作为基础镜像
FROM openjdk:17-jdk-slim

# 设置作者信息
LABEL maintainer="FangGL"
LABEL description="XXL-Job分布式任务调度平台 - PostgreSQL版本"
LABEL version="3.2.0"

# 配置中国镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装必要的系统工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    maven \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 创建应用用户（安全最佳实践）
RUN groupadd -r xxljob && useradd -r -g xxljob xxljob

# 创建必要的目录结构
RUN mkdir -p /app/logs/xxl-job && \
    mkdir -p /app/config && \
    chown -R xxljob:xxljob /app

# 配置Maven settings.xml
COPY build/settings.xml /root/.m2/settings.xml

# 复制项目文件
COPY . /app/source/

# 切换到源码目录并构建
WORKDIR /app/source
RUN mvn clean package -DskipTests

# 复制构建好的jar文件到工作目录
RUN ls -la xxl-job-admin/target/ && \
    find xxl-job-admin/target/ -name "*.jar" && \
    rm -f /app/xxl-job-admin.jar && \
    cp xxl-job-admin/target/xxl-job-admin-3.2.0.jar /app/xxl-job-admin.jar

# 切换回工作目录
WORKDIR /app

# 清理构建临时文件
RUN rm -rf /app/source && apt-get remove -y maven && apt-get autoremove -y

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 创建日志目录\n\
mkdir -p /app/logs/xxl-job\n\
\n\
# 启动应用\n\
exec java $JAVA_OPTS -jar /app/xxl-job-admin.jar\n\
' > /app/start.sh && chmod +x /app/start.sh

# 更改文件所有权
RUN chown -R xxljob:xxljob /app

# 切换到应用用户
USER xxljob

# 暴露端口
EXPOSE 8898

# 设置环境变量默认值
ENV JAVA_OPTS="-Xmx512m -Xms256m"
ENV TZ="Asia/Shanghai"

# 应用配置环境变量
ENV SERVER_PORT="8898"
ENV CONTEXT_PATH="/xxl-job-admin"
ENV XXL_JOB_ACCESS_TOKEN="default_token"
ENV XXL_JOB_TIMEOUT="3"
ENV XXL_JOB_I18N="zh_CN"

# 启动命令
CMD ["/app/start.sh"]
