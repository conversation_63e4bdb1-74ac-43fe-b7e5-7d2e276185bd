# XXL-Job Docker 部署指南

## 作者信息
- **作者**: FangGL
- **日期**: 2024-12-27

## 概述

本项目提供了完整的Docker化部署方案，基于OpenJDK 17，支持PostgreSQL数据库，通过环境变量进行灵活配置。

## 快速开始

### 1. 使用Docker Compose部署（推荐）

```bash
# 启动完整服务（包含PostgreSQL和XXL-Job管理后台）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f xxl-job-admin
```

### 2. 单独构建和运行

#### 构建镜像
```bash
# 构建管理后台镜像
docker build -t xxl-job-admin:3.2.0 .


```

#### 运行容器
```bash
# 启动PostgreSQL数据库
docker run -d \
  --name xxl-job-postgresql \
  -e POSTGRES_DB=xxl_job \
  -e POSTGRES_USER=yunzheng \
  -e POSTGRES_PASSWORD=yunzheng \
  -p 5434:5432 \
  -v $(pwd)/doc/db/tables_xxl_job_postgresql.sql:/docker-entrypoint-initdb.d/init.sql:ro \
  postgres:15-alpine

# 启动XXL-Job管理后台
docker run -d \
  --name xxl-job-admin \
  --link xxl-job-postgresql:postgresql \
  -e DATASOURCE_URL="*****************************************" \
  -e DATASOURCE_USERNAME="yunzheng" \
  -e DATASOURCE_PASSWORD="yunzheng" \
  -p 8898:8898 \
  xxl-job-admin:3.2.0
```

## 环境变量配置

### 数据库配置
| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `DATASOURCE_URL` | `********************************************` | 数据库连接URL |
| `DATASOURCE_USERNAME` | `yunzheng` | 数据库用户名 |
| `DATASOURCE_PASSWORD` | `yunzheng` | 数据库密码 |
| `DATASOURCE_DRIVER` | `org.postgresql.Driver` | 数据库驱动类 |

### 应用配置
| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `SERVER_PORT` | `8898` | 应用服务端口 |
| `CONTEXT_PATH` | `/xxl-job-admin` | 应用上下文路径 |
| `XXL_JOB_ACCESS_TOKEN` | `default_token` | 访问令牌 |
| `XXL_JOB_TIMEOUT` | `3` | 任务超时时间（秒） |
| `XXL_JOB_I18N` | `zh_CN` | 国际化语言 |

### JVM配置
| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `JAVA_OPTS` | `-Xmx512m -Xms256m` | JVM参数 |

### 执行器配置（仅执行器）
| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `XXL_JOB_ADMIN_ADDRESSES` | `http://xxl-job-admin:8898/xxl-job-admin` | 管理后台地址 |
| `XXL_JOB_EXECUTOR_APPNAME` | `xxl-job-executor-sample` | 执行器应用名 |
| `XXL_JOB_EXECUTOR_PORT` | `9999` | 执行器端口 |
| `XXL_JOB_EXECUTOR_LOGPATH` | `/app/logs` | 日志路径 |
| `XXL_JOB_EXECUTOR_LOGRETENTIONDAYS` | `30` | 日志保留天数 |

## 自定义配置示例

### 1. 生产环境配置
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  xxl-job-admin:
    image: xxl-job-admin:3.2.0
    environment:
      JAVA_OPTS: "-Xmx2g -Xms1g -XX:+UseG1GC"
      DATASOURCE_URL: "**************************************"
      DATASOURCE_USERNAME: "xxljob_user"
      DATASOURCE_PASSWORD: "secure_password"
      XXL_JOB_ACCESS_TOKEN: "production_secret_token"
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

### 2. 开发环境配置
```bash
# 使用环境变量文件
echo "DATASOURCE_URL=********************************************" > .env
echo "DATASOURCE_USERNAME=dev_user" >> .env
echo "DATASOURCE_PASSWORD=dev_pass" >> .env

docker-compose --env-file .env up -d
```

## 数据持久化

### 数据卷说明
- `postgresql_data`: PostgreSQL数据文件
- `xxl_job_logs`: XXL-Job管理后台日志
- `executor_logs`: 执行器日志

### 备份数据
```bash
# 备份PostgreSQL数据
docker exec xxl-job-postgresql pg_dump -U yunzheng xxl_job > backup_$(date +%Y%m%d).sql

# 备份数据卷
docker run --rm -v xxl-job-postgresql-data:/data -v $(pwd):/backup alpine tar czf /backup/postgresql_backup_$(date +%Y%m%d).tar.gz /data
```

## 健康检查

容器内置健康检查，可通过以下方式查看状态：

```bash
# 查看容器健康状态
docker ps

# 手动健康检查
docker exec xxl-job-admin curl -f http://localhost:8898/xxl-job-admin/actuator/health
```

## 故障排除

### 1. 启动失败
```bash
# 查看容器日志
docker logs xxl-job-admin

# 进入容器调试
docker exec -it xxl-job-admin /bin/bash
```

### 2. 数据库连接问题
```bash
# 检查数据库连接
docker exec xxl-job-admin nc -z postgresql 5432

# 查看数据库日志
docker logs xxl-job-postgresql
```

### 3. 内存不足
```bash
# 调整JVM参数
docker run -e JAVA_OPTS="-Xmx1g -Xms512m" xxl-job-admin:3.2.0
```

## 访问应用

- **管理后台**: http://localhost:8898/xxl-job-admin
- **默认账户**: admin / 123456


## 网络配置

容器使用自定义网络 `xxl-job-network`，确保服务间可以通过服务名互相访问。

## 安全建议

1. **修改默认密码**: 更改管理员密码和数据库密码
2. **使用强访问令牌**: 设置复杂的 `XXL_JOB_ACCESS_TOKEN`
3. **限制网络访问**: 仅暴露必要的端口
4. **定期备份**: 设置自动备份策略
5. **监控日志**: 配置日志监控和告警

## 扩展配置

### 使用外部数据库
```yaml
services:
  xxl-job-admin:
    environment:
      DATASOURCE_URL: "******************************************************"
      DATASOURCE_USERNAME: "external_user"
      DATASOURCE_PASSWORD: "external_password"
```

### 集群部署
```yaml
services:
  xxl-job-admin-1:
    image: xxl-job-admin:3.2.0
    ports:
      - "8898:8898"
  
  xxl-job-admin-2:
    image: xxl-job-admin:3.2.0
    ports:
      - "8899:8898"
```

## 版本升级

```bash
# 停止服务
docker-compose down

# 备份数据
docker run --rm -v xxl-job-postgresql-data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz /data

# 拉取新版本
docker-compose pull

# 启动新版本
docker-compose up -d
```
